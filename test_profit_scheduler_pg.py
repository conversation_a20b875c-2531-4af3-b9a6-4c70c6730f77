#!/usr/bin/env python3
"""
测试脚本：验证profit_data_scheduler_pg.py从MySQL改为PostgreSQL后的功能
"""

import asyncio
import os
import sys
from datetime import datetime, date

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.app.profit_data_scheduler_pg import ProfitDataScheduler, AnalysisPeriod
from utils.basic.logger_config import setup_logger

logger = setup_logger(
    name=__name__,
    level="INFO",
    log_file="test_profit_scheduler_pg.log"
)

async def test_database_connection():
    """测试PostgreSQL数据库连接"""
    logger.info("=== 测试数据库连接 ===")
    try:
        from utils.basic.pg_conn import get_postgres_connection_async, PG_DB_CMSDATA
        
        async with get_postgres_connection_async(database=PG_DB_CMSDATA) as connection:
            # 测试简单查询
            result = await connection.fetchval("SELECT 1")
            logger.info(f"数据库连接测试成功，查询结果: {result}")
            return True
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        return False

async def test_table_creation():
    """测试表创建功能"""
    logger.info("=== 测试表创建功能 ===")
    try:
        scheduler = ProfitDataScheduler()
        await scheduler.ensure_required_tables_exist()
        logger.info("表创建测试成功")
        return True
    except Exception as e:
        logger.error(f"表创建测试失败: {e}")
        return False

async def test_environment_validation():
    """测试环境变量验证"""
    logger.info("=== 测试环境变量验证 ===")
    try:
        scheduler = ProfitDataScheduler()
        # 这会在初始化时自动调用_validate_environment
        logger.info("环境变量验证测试成功")
        return True
    except Exception as e:
        logger.error(f"环境变量验证测试失败: {e}")
        return False

async def test_scheduler_check_log():
    """测试调度器检查日志功能"""
    logger.info("=== 测试调度器检查日志功能 ===")
    try:
        scheduler = ProfitDataScheduler()
        
        # 创建测试周期
        test_period = AnalysisPeriod(
            start_date=date(2024, 1, 1),
            end_date=date(2024, 1, 31),
            period_type="test",
            check_frequency="1week"
        )
        
        # 测试获取最后检查时间（应该返回None，因为是新记录）
        last_check = await scheduler.get_last_check_time(test_period)
        logger.info(f"获取最后检查时间: {last_check}")
        
        # 测试更新最后检查时间
        current_time = datetime.now()
        await scheduler.update_last_check_time(test_period, current_time, "test_job_hash", "test_booking_hash")
        logger.info("更新最后检查时间成功")
        
        # 再次获取最后检查时间，应该返回刚才设置的时间
        last_check_after = await scheduler.get_last_check_time(test_period)
        logger.info(f"更新后的最后检查时间: {last_check_after}")
        
        if last_check_after is not None:
            logger.info("调度器检查日志功能测试成功")
            return True
        else:
            logger.error("更新后未能获取到检查时间")
            return False
            
    except Exception as e:
        logger.error(f"调度器检查日志功能测试失败: {e}")
        return False

async def test_data_hash_functions():
    """测试数据哈希功能"""
    logger.info("=== 测试数据哈希功能 ===")
    try:
        scheduler = ProfitDataScheduler()
        
        # 测试数据
        test_data = [
            {"job_no": "TEST001", "profit": 1000.50, "client_name": "测试客户1"},
            {"job_no": "TEST002", "profit": 2000.75, "client_name": "测试客户2"}
        ]
        
        # 测试计算数据哈希
        job_hash = await scheduler.calculate_data_hash(test_data, "job")
        booking_hash = await scheduler.calculate_data_hash(test_data, "booking")
        
        logger.info(f"Job数据哈希: {job_hash}")
        logger.info(f"Booking数据哈希: {booking_hash}")
        
        # 测试单条记录哈希
        record_hash = await scheduler.calculate_record_hash(test_data[0], "job")
        logger.info(f"单条记录哈希: {record_hash}")
        
        if job_hash and booking_hash and record_hash:
            logger.info("数据哈希功能测试成功")
            return True
        else:
            logger.error("哈希计算返回空值")
            return False
            
    except Exception as e:
        logger.error(f"数据哈希功能测试失败: {e}")
        return False

async def test_data_cleaning():
    """测试数据清理功能"""
    logger.info("=== 测试数据清理功能 ===")
    try:
        scheduler = ProfitDataScheduler()
        
        # 测试clean_data_for_postgres方法
        test_values = [
            None,
            float('nan'),
            float('inf'),
            "正常字符串",
            123.45,
            0
        ]
        
        for value in test_values:
            cleaned = scheduler.clean_data_for_postgres(value)
            logger.info(f"原值: {value} -> 清理后: {cleaned}")
        
        # 测试safe_int_convert方法
        test_int_values = [
            "123",
            "123.45",
            None,
            "",
            "abc",
            9223372036854775808  # 超出bigint范围
        ]
        
        for value in test_int_values:
            converted = scheduler.safe_int_convert(value)
            logger.info(f"整数转换 - 原值: {value} -> 转换后: {converted}")
        
        logger.info("数据清理功能测试成功")
        return True
        
    except Exception as e:
        logger.error(f"数据清理功能测试失败: {e}")
        return False

async def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行PostgreSQL迁移测试")
    
    tests = [
        ("数据库连接", test_database_connection),
        ("环境变量验证", test_environment_validation),
        ("表创建功能", test_table_creation),
        ("调度器检查日志", test_scheduler_check_log),
        ("数据哈希功能", test_data_hash_functions),
        ("数据清理功能", test_data_cleaning),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n开始测试: {test_name}")
        try:
            result = await test_func()
            if result:
                logger.info(f"✅ {test_name} - 通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} - 失败")
                failed += 1
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
            failed += 1
    
    logger.info(f"\n=== 测试总结 ===")
    logger.info(f"通过: {passed}")
    logger.info(f"失败: {failed}")
    logger.info(f"总计: {passed + failed}")
    
    if failed == 0:
        logger.info("🎉 所有测试通过！PostgreSQL迁移成功！")
    else:
        logger.warning(f"⚠️  有 {failed} 个测试失败，需要检查问题")
    
    return failed == 0

if __name__ == "__main__":
    # 检查必要的环境变量
    required_env_vars = ['PG_HOST', 'PG_USER', 'PG_PASSWORD', 'PG_DB_CMSDATA']
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"缺少必要的环境变量: {missing_vars}")
        logger.error("请设置以下环境变量后重新运行测试:")
        for var in missing_vars:
            logger.error(f"  export {var}=<your_value>")
        sys.exit(1)
    
    # 运行测试
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)
